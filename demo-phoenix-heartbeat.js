console.log('🫀 Phoenix Channel 心跳格式演示\n');

// 演示心跳消息的生成逻辑
function generatePhoenixHeartbeat(counter) {
  return [null, counter.toString(), "phoenix", "heartbeat", {}];
}

// 演示从 7 开始的心跳序列
function demonstrateHeartbeatSequence() {
  console.log('📋 Phoenix Channel 心跳消息格式演示:\n');
  
  console.log('🔍 心跳消息结构:');
  console.log('   [引用, 计数器, 协议, 事件, 负载]');
  console.log('   [null, "N", "phoenix", "heartbeat", {}]\n');
  
  console.log('💓 心跳序列（从 7 开始，每30秒递增）:\n');
  
  for (let i = 7; i <= 15; i++) {
    const heartbeat = generatePhoenixHeartbeat(i);
    const jsonString = JSON.stringify(heartbeat);
    
    console.log(`   第 ${i - 6} 次 (${(i - 6) * 30}秒后): ${jsonString}`);
    
    // 详细解释前几个心跳
    if (i <= 9) {
      console.log(`           ↳ 引用: ${heartbeat[0]} (null)`);
      console.log(`           ↳ 计数器: "${heartbeat[1]}" (字符串格式)`);
      console.log(`           ↳ 协议: "${heartbeat[2]}" (phoenix)`);
      console.log(`           ↳ 事件: "${heartbeat[3]}" (heartbeat)`);
      console.log(`           ↳ 负载: ${JSON.stringify(heartbeat[4])} (空对象)`);
      console.log('');
    }
  }
  
  console.log('   ...');
  console.log('   (持续递增，直到连接断开)\n');
}

// 验证心跳格式的函数
function validateHeartbeatFormat(heartbeat) {
  const errors = [];
  
  // 检查是否是数组
  if (!Array.isArray(heartbeat)) {
    errors.push('心跳消息必须是数组格式');
    return errors;
  }
  
  // 检查数组长度
  if (heartbeat.length !== 5) {
    errors.push(`心跳消息必须包含 5 个元素，实际包含 ${heartbeat.length} 个`);
    return errors;
  }
  
  const [ref, counter, protocol, event, payload] = heartbeat;
  
  // 检查第 1 个元素：引用
  if (ref !== null) {
    errors.push(`第 1 个元素（引用）必须是 null，实际是: ${ref}`);
  }
  
  // 检查第 2 个元素：计数器
  if (typeof counter !== 'string') {
    errors.push(`第 2 个元素（计数器）必须是字符串，实际类型: ${typeof counter}`);
  } else if (!/^\d+$/.test(counter)) {
    errors.push(`第 2 个元素（计数器）必须是数字字符串，实际值: "${counter}"`);
  }
  
  // 检查第 3 个元素：协议
  if (protocol !== 'phoenix') {
    errors.push(`第 3 个元素（协议）必须是 "phoenix"，实际是: "${protocol}"`);
  }
  
  // 检查第 4 个元素：事件
  if (event !== 'heartbeat') {
    errors.push(`第 4 个元素（事件）必须是 "heartbeat"，实际是: "${event}"`);
  }
  
  // 检查第 5 个元素：负载
  if (typeof payload !== 'object' || payload === null) {
    errors.push(`第 5 个元素（负载）必须是对象，实际类型: ${typeof payload}`);
  } else if (Object.keys(payload).length !== 0) {
    errors.push(`第 5 个元素（负载）必须是空对象 {}，实际包含键: [${Object.keys(payload).join(', ')}]`);
  }
  
  return errors;
}

// 演示格式验证
function demonstrateValidation() {
  console.log('🔍 心跳格式验证演示:\n');
  
  const testCases = [
    {
      name: '正确格式',
      heartbeat: [null, "7", "phoenix", "heartbeat", {}]
    },
    {
      name: '错误的引用（应该是 null）',
      heartbeat: ["ref", "7", "phoenix", "heartbeat", {}]
    },
    {
      name: '错误的计数器类型（应该是字符串）',
      heartbeat: [null, 7, "phoenix", "heartbeat", {}]
    },
    {
      name: '错误的协议名称',
      heartbeat: [null, "7", "Phoenix", "heartbeat", {}]
    },
    {
      name: '错误的事件名称',
      heartbeat: [null, "7", "phoenix", "ping", {}]
    },
    {
      name: '非空负载对象',
      heartbeat: [null, "7", "phoenix", "heartbeat", { data: "test" }]
    }
  ];
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}:`);
    console.log(`   消息: ${JSON.stringify(testCase.heartbeat)}`);
    
    const errors = validateHeartbeatFormat(testCase.heartbeat);
    if (errors.length === 0) {
      console.log(`   ✅ 格式正确`);
    } else {
      console.log(`   ❌ 格式错误:`);
      errors.forEach(error => console.log(`      - ${error}`));
    }
    console.log('');
  });
}

// 演示实际的代码实现
function demonstrateImplementation() {
  console.log('💻 代码实现演示:\n');
  
  console.log('```javascript');
  console.log('// Phoenix Channel 心跳生成函数');
  console.log('function startPhoenixHeartbeat(connectionId) {');
  console.log('  let counter = 7; // 从 7 开始');
  console.log('  ');
  console.log('  const timer = setInterval(() => {');
  console.log('    // 生成心跳消息');
  console.log('    const heartbeatMessage = [');
  console.log('      null,                    // 引用字段');
  console.log('      counter.toString(),      // 计数器（字符串格式）');
  console.log('      "phoenix",              // 协议标识');
  console.log('      "heartbeat",            // 事件类型');
  console.log('      {}                      // 空负载');
  console.log('    ];');
  console.log('    ');
  console.log('    // 发送到目标服务器');
  console.log('    const messageString = JSON.stringify(heartbeatMessage);');
  console.log('    connectionInfo.ws.send(messageString);');
  console.log('    ');
  console.log('    // 递增计数器');
  console.log('    counter++;');
  console.log('  }, 30000); // 每30秒执行一次');
  console.log('}');
  console.log('```\n');
}

// 主函数
function main() {
  console.log('🎯 Phoenix Channel 心跳完整演示\n');
  
  // 1. 演示心跳序列
  demonstrateHeartbeatSequence();
  
  // 2. 演示格式验证
  demonstrateValidation();
  
  // 3. 演示代码实现
  demonstrateImplementation();
  
  console.log('📋 关键要点总结:');
  console.log('   ✅ 心跳从计数器 7 开始');
  console.log('   ✅ 每30秒递增 1: 7 → 8 → 9 → 10 → ...');
  console.log('   ✅ 计数器必须是字符串格式: "7", "8", "9"');
  console.log('   ✅ 固定格式: [null, "N", "phoenix", "heartbeat", {}]');
  console.log('   ✅ 第 1 个元素必须是 null');
  console.log('   ✅ 第 3 个元素必须是 "phoenix"');
  console.log('   ✅ 第 4 个元素必须是 "heartbeat"');
  console.log('   ✅ 第 5 个元素必须是空对象 {}');
  
  console.log('\n🧪 验证命令:');
  console.log('   npm run verify:phoenix  # 验证心跳格式');
  console.log('   npm run test:phoenix    # 完整功能测试');
  
  console.log('\n🎉 Phoenix Channel 心跳演示完成！');
}

// 运行演示
if (require.main === module) {
  main();
}
