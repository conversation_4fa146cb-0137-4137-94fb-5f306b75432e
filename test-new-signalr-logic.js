// 简单测试新的 SignalR 逻辑
const WebSocket = require('ws');

console.log('🧪 测试新的 SignalR 订阅逻辑');
console.log('📝 预期行为:');
console.log('   1. 客户端连接到 SignalR 代理 (target=1)');
console.log('   2. 代理服务器代替客户端发送订阅消息');
console.log('   3. 客户端收到系统消息确认订阅已发送');
console.log('   4. 后续数据按广播逻辑发送给所有客户端');
console.log('');

// 连接到 SignalR 目标
const ws = new WebSocket('ws://localhost:8080?target=1');

ws.on('open', () => {
  console.log('✅ 已连接到代理服务器 SignalR 目标 (ID: 1)');
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    
    if (message.type === 'system') {
      console.log(`📢 [系统消息] ${message.message}`);
      
      if (message.subscription) {
        console.log('📋 订阅内容:', message.subscription);
      }
      
      if (message.clientId) {
        console.log(`👤 客户端 ID: ${message.clientId}`);
      }
    } else {
      console.log('📥 [数据消息] 收到数据:', {
        size: data.length,
        preview: data.toString().substring(0, 200) + (data.length > 200 ? '...' : '')
      });
    }
  } catch (e) {
    console.log('📥 [原始数据] 收到非 JSON 数据:', {
      size: data.length,
      preview: data.toString().substring(0, 200) + (data.length > 200 ? '...' : '')
    });
  }
});

ws.on('close', (code, reason) => {
  console.log(`❌ 连接关闭: ${code} - ${reason || '无原因'}`);
  process.exit(0);
});

ws.on('error', (error) => {
  console.error('❌ 连接错误:', error.message);
  process.exit(1);
});

// 10秒后自动关闭
setTimeout(() => {
  console.log('\n⏰ 测试时间结束，关闭连接');
  ws.close();
}, 10000);

console.log('⏳ 等待连接建立...');
