const WebSocket = require('ws');
const express = require('express');
const cors = require('cors');
const http = require('http');
const https = require('https');

// 配置
const CONFIG = {
  PORT: process.env.PORT || 8080,
  TARGET_WS_URL: process.env.TARGET_WS_URL || 'ws://echo.websocket.org',
  ENABLE_LOGGING: process.env.ENABLE_LOGGING !== 'false',
  RECONNECT_INTERVAL: 5000, // 重连间隔（毫秒）
  MAX_RECONNECT_ATTEMPTS: 10, // 最大重连次数
  AUTO_CONNECT: true, // 固定为自动连接

  // SignalR 配置
  SIGNALR_URL: process.env.SIGNALR_URL || 'livetiming.formula1.com/signalr',
  SIGNALR_HUB: process.env.SIGNALR_HUB || 'Streaming',
  SIGNALR_PROTOCOL: process.env.SIGNALR_PROTOCOL || '1.5',
  USER_AGENT: process.env.USER_AGENT || 'BestHTTP'
};

// 动态配置（可通过 API 修改）
let dynamicConfig = {
  autoReconnect: true
};

// 创建 Express 应用
const app = express();
app.use(cors());
app.use(express.json());

// 创建 HTTP 服务器
const server = http.createServer(app);

// 创建 WebSocket 服务器
const wss = new WebSocket.Server({ server });

// 日志函数
function log(message, ...args) {
  if (CONFIG.ENABLE_LOGGING) {
    console.log(`[${new Date().toISOString()}] ${message}`, ...args);
  }
}

// 存储客户端连接
const clients = new Map();
let clientId = 0;

// 固定的两个目标连接
const FIXED_CONNECTIONS = {
  SIGNALR_ID: 1,
  WEBSOCKET_ID: 2
};

const targetConnections = new Map(); // key: connectionId, value: connection info

// Phoenix Channel 心跳管理
const phoenixHeartbeats = new Map(); // key: connectionId, value: { timer, counter }

// 连接类型
const CONNECTION_TYPES = {
  WEBSOCKET: 'websocket',
  SIGNALR: 'signalr'
};

// 客户端分组管理 - 按目标连接分组
const clientGroups = new Map(); // key: targetConnectionId, value: Set of client IDs

// 全局状态（兼容旧代码）
let targetConnection = null;
let reconnectAttempts = 0;
let reconnectTimer = null;

// SignalR 协商函数
function negotiateSignalR(signalrConfig) {
  return new Promise((resolve, reject) => {
    const hub = encodeURIComponent(JSON.stringify([{ name: signalrConfig.hub }]));
    const negotiateUrl = `https://${signalrConfig.url}/negotiate?connectionData=${hub}&clientProtocol=${signalrConfig.protocol}`;

    log(`正在进行 SignalR 协商: ${negotiateUrl}`);

    https.get(negotiateUrl, {
      headers: {
        'User-Agent': CONFIG.USER_AGENT,
        'Accept-Encoding': 'gzip,identity'
      }
    }, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          const connectionToken = result.ConnectionToken;
          const cookie = res.headers['set-cookie'];

          if (!connectionToken) {
            reject(new Error('未能获取 SignalR ConnectionToken'));
            return;
          }

          log(`SignalR 协商成功，获取到 ConnectionToken: ${connectionToken.substring(0, 20)}...`);
          resolve({ connectionToken, cookie });

        } catch (error) {
          reject(new Error(`解析 SignalR 协商响应失败: ${error.message}`));
        }
      });

    }).on('error', (error) => {
      reject(new Error(`SignalR 协商请求失败: ${error.message}`));
    });
  });
}

// Phoenix Channel 心跳管理函数
function startPhoenixHeartbeat(connectionId) {
  // 停止现有的心跳（如果有）
  stopPhoenixHeartbeat(connectionId);

  const connectionInfo = targetConnections.get(connectionId);
  if (!connectionInfo) {
    log(`无法启动心跳: 目标连接 #${connectionId} 不存在`);
    return;
  }

  log(`🫀 启动 Phoenix Channel 心跳 - 目标连接 #${connectionId}: ${connectionInfo.name} (每30秒)`);

  let counter = 7; // 从 7 开始

  const timer = setInterval(() => {
    if (connectionInfo.ws.readyState === WebSocket.OPEN) {
      const heartbeatMessage = [null, counter.toString(), "phoenix", "heartbeat", {}];
      const messageString = JSON.stringify(heartbeatMessage);

      log(`💓 发送 Phoenix 心跳 #${counter} 到目标 #${connectionId}: ${messageString}`);

      try {
        connectionInfo.ws.send(messageString);
        counter++;
      } catch (error) {
        log(`❌ 发送 Phoenix 心跳失败: ${error.message}`);
        stopPhoenixHeartbeat(connectionId);
      }
    } else {
      log(`⚠️ 目标连接 #${connectionId} 已断开，停止心跳`);
      stopPhoenixHeartbeat(connectionId);
    }
  }, 30000); // 每30秒发送一次

  // 存储心跳信息
  phoenixHeartbeats.set(connectionId, {
    timer: timer,
    counter: counter
  });
}

function stopPhoenixHeartbeat(connectionId) {
  const heartbeatInfo = phoenixHeartbeats.get(connectionId);
  if (heartbeatInfo) {
    clearInterval(heartbeatInfo.timer);
    phoenixHeartbeats.delete(connectionId);

    const connectionInfo = targetConnections.get(connectionId);
    const connectionName = connectionInfo ? connectionInfo.name : `#${connectionId}`;
    log(`💔 停止 Phoenix Channel 心跳 - 目标连接 ${connectionName}`);
  }
}



// 创建目标连接
async function createTargetConnection(config) {
  const { type, name, url, signalr, fixedId } = config;
  const connectionId = fixedId || (type === CONNECTION_TYPES.SIGNALR ? FIXED_CONNECTIONS.SIGNALR_ID : FIXED_CONNECTIONS.WEBSOCKET_ID);

  // 检查是否已存在相同ID的连接
  if (targetConnections.has(connectionId)) {
    log(`目标连接 #${connectionId} 已存在，先断开旧连接`);
    disconnectTarget(connectionId);
  }

  log(`正在创建目标连接 #${connectionId}: ${name} (${type})`);

  try {
    let wsUrl;
    let headers = {};
    let connectionToken = null;
    let cookie = null;

    if (type === CONNECTION_TYPES.SIGNALR) {
      log(`正在连接到 SignalR 服务器: ${signalr.url}`);

      // 进行 SignalR 协商
      const negotiationResult = await negotiateSignalR(signalr);
      connectionToken = negotiationResult.connectionToken;
      cookie = negotiationResult.cookie;

      // 构建 SignalR WebSocket URL
      const hub = encodeURIComponent(JSON.stringify([{ name: signalr.hub }]));
      wsUrl = `wss://${signalr.url}/connect?clientProtocol=${signalr.protocol}&transport=webSockets&connectionToken=${encodeURIComponent(connectionToken)}&connectionData=${hub}`;

      headers = {
        'User-Agent': CONFIG.USER_AGENT,
        'Accept-Encoding': 'gzip,identity'
      };

      if (cookie) {
        headers['Cookie'] = Array.isArray(cookie) ? cookie.join('; ') : cookie;
      }

    } else {
      wsUrl = url;
      log(`正在连接到 WebSocket 服务器: ${wsUrl}`);
    }

    const ws = new WebSocket(wsUrl, { headers });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000); // 10秒超时

      ws.on('open', () => {
        clearTimeout(timeout);
        log(`✅ 目标连接 #${connectionId} 成功建立`);

        // 存储连接信息
        const connectionInfo = {
          id: connectionId,
          name,
          type,
          url: wsUrl,
          ws,
          config: type === CONNECTION_TYPES.SIGNALR ? signalr : { url },
          connectionToken,
          cookie,
          startTime: Date.now(),
          messagesReceived: 0,
          autoReconnect: true,
          // SignalR 初始化消息缓存
          initMessage: null,
          hasReceivedInit: false
        };

        targetConnections.set(connectionId, connectionInfo);
        clientGroups.set(connectionId, new Set());

        // 设置消息处理
        ws.on('message', (data, isBinary) => {
          connectionInfo.messagesReceived++;
          const messageStr = data.toString();

          log(`📥 目标连接 #${connectionId} (${connectionInfo.name}) 收到消息:`);
          log(`   数据大小: ${data.length} 字节`);
          log(`   二进制模式: ${isBinary ? '是' : '否'}`);

          // 尝试解析和美化显示消息内容
          if (!isBinary) {
            try {
              const parsed = JSON.parse(messageStr);
              log(`   JSON 消息内容:`);
              log(`${JSON.stringify(parsed, null, 2)}`);

              // 检查是否是 Phoenix Channel 的 phx_reply 消息且状态为 ok
              // 但排除心跳消息的回复，避免循环触发
              if (Array.isArray(parsed) && parsed.length >= 4) {
                const [ref, , topic, event, payload] = parsed;
                if (event === "phx_reply" && payload && payload.status === "ok") {
                  // 检查是否已经有心跳在运行，避免重复启动
                  if (!phoenixHeartbeats.has(connectionId)) {
                    // 只在非心跳回复时启动心跳（心跳回复的 ref 通常是 null）
                    // 而 phx_join 等操作的回复 ref 通常是数字字符串
                    if (ref !== null && ref !== "null") {
                      log(`🔥 检测到 Phoenix Channel phx_reply 状态为 ok (ref: ${ref})，启动心跳机制`);
                      startPhoenixHeartbeat(connectionId);
                    } else {
                      log(`💓 收到心跳回复，跳过重复启动 (ref: ${ref})`);
                    }
                  } else {
                    log(`💓 心跳已在运行，跳过重复启动`);
                  }
                }
              }

            } catch (e) {
              // 不是 JSON 格式，显示原始文本
              const preview = messageStr.length > 200 ? messageStr.substring(0, 200) + '...' : messageStr;
              log(`   文本消息内容: ${preview}`);
            }
          } else {
            log(`   二进制消息内容: [${data.length} 字节的二进制数据]`);
          }

          // 检查是否是 SignalR 初始化消息
          if (type === CONNECTION_TYPES.SIGNALR && !connectionInfo.hasReceivedInit) {
            if (messageStr.startsWith('{"R":{"Heartbeat":')) {
              connectionInfo.initMessage = data.toString();
              connectionInfo.hasReceivedInit = true;
              log(`📌 缓存 SignalR 初始化消息 (${data.length} 字节)`);
            }
          }

          // 广播给连接到此目标的客户端
          const clientCount = clientGroups.get(connectionId)?.size || 0;
          log(`📤 广播消息给 ${clientCount} 个客户端`);
          broadcastToTargetClients(connectionId, data, isBinary);
        });

        ws.on('close', (code, reason) => {
          log(`目标连接 #${connectionId} 关闭 (${code}: ${reason || '无'})`);

          // 通知连接到此目标的客户端
          broadcastToTargetClients(connectionId, JSON.stringify({
            type: 'system',
            message: `目标服务器 ${name} 连接已断开`,
            code: code,
            reason: reason || '无'
          }));

          // 清理连接
          targetConnections.delete(connectionId);
          clientGroups.delete(connectionId);

          // 停止 Phoenix 心跳（如果有）
          stopPhoenixHeartbeat(connectionId);
        });

        ws.on('error', (error) => {
          log(`目标连接 #${connectionId} 错误: ${error.message}`);
        });

        // 如果是 SignalR 连接，自动发送订阅
        if (type === CONNECTION_TYPES.SIGNALR) {
          setTimeout(() => {
            sendSignalRSubscriptionToTarget(connectionId);
          }, 1000);
        }

        resolve(connectionInfo);
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });

  } catch (error) {
    log(`创建目标连接失败: ${error.message}`);
    throw error;
  }
}

// 广播消息给所有客户端
function broadcastToClients(data, isBinary = false) {
  const deadClients = [];

  clients.forEach((clientInfo, id) => {
    const client = clientInfo.ws;
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(data, { binary: isBinary });
        clientInfo.messagesReceived++;
      } catch (error) {
        log(`向客户端 #${id} 发送消息失败: ${error.message}`);
        deadClients.push(id);
      }
    } else {
      deadClients.push(id);
    }
  });

  // 清理断开的客户端
  deadClients.forEach(id => {
    clients.delete(id);
    log(`清理断开的客户端 #${id}`);
  });
}

// 广播消息给特定目标连接的客户端
function broadcastToTargetClients(targetConnectionId, data, isBinary = false) {
  const clientSet = clientGroups.get(targetConnectionId);
  if (!clientSet) return;

  const deadClients = [];

  clientSet.forEach(clientId => {
    const clientInfo = clients.get(clientId);
    if (clientInfo && clientInfo.ws.readyState === WebSocket.OPEN) {
      try {
        clientInfo.ws.send(data, { binary: isBinary });
        clientInfo.messagesReceived++;
      } catch (error) {
        log(`向客户端 #${clientId} 发送消息失败: ${error.message}`);
        deadClients.push(clientId);
      }
    } else {
      deadClients.push(clientId);
    }
  });

  // 清理断开的客户端
  deadClients.forEach(clientId => {
    clientSet.delete(clientId);
    clients.delete(clientId);
    log(`清理断开的客户端 #${clientId}`);
  });
}

// 发送 SignalR 订阅消息到特定目标连接
function sendSignalRSubscriptionToTarget(targetConnectionId) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo || connectionInfo.type !== CONNECTION_TYPES.SIGNALR) {
    return false;
  }

  if (connectionInfo.ws.readyState !== WebSocket.OPEN) {
    return false;
  }

  const subscriptionMessage = {
    H: connectionInfo.config.hub,
    M: "Subscribe",
    A: [
      [
        "Heartbeat",
        "CarData.z",
        "Position.z",
        "ExtrapolatedClock",
        "TimingStats",
        "TimingAppData",
        "WeatherData",
        "TrackStatus",
        "DriverList",
        "RaceControlMessages",
        "SessionInfo",
        "SessionData",
        "LapCount",
        "TimingData",
        "TeamRadio",
      ],
    ],
    I: 1,
  };

  try {
    const messageStr = JSON.stringify(subscriptionMessage);
    connectionInfo.ws.send(messageStr);
    log(`📡 已向目标连接 #${targetConnectionId} 发送 SignalR 订阅消息`);

    // 通知连接到此目标的客户端
    broadcastToTargetClients(targetConnectionId, JSON.stringify({
      type: 'system',
      message: 'SignalR 订阅消息已发送',
      subscription: subscriptionMessage.A[0]
    }));

    return true;
  } catch (error) {
    log(`向目标连接 #${targetConnectionId} 发送 SignalR 订阅消息失败: ${error.message}`);
    return false;
  }
}

// 获取可用的目标连接列表
function getAvailableTargets() {
  return Array.from(targetConnections.values()).map(conn => ({
    id: conn.id,
    name: conn.name,
    type: conn.type,
    url: conn.url,
    status: conn.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected',
    clientCount: clientGroups.get(conn.id)?.size || 0,
    messagesReceived: conn.messagesReceived,
    uptime: Date.now() - conn.startTime,
    // SignalR 特有信息
    hasInitMessage: conn.type === CONNECTION_TYPES.SIGNALR ? conn.hasReceivedInit : null
  }));
}

// 转发消息到目标服务器
function forwardMessageToTarget(targetConnectionId, messageData, isBinary = false) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo) {
    log(`转发失败: 目标连接 #${targetConnectionId} 不存在`);
    return false;
  }

  if (connectionInfo.ws.readyState !== WebSocket.OPEN) {
    log(`转发失败: 目标连接 #${targetConnectionId} 未连接`);
    return false;
  }

  try {
    // 如果 messageData 是对象，转换为 JSON 字符串
    let dataToSend;
    let isJsonData = false;

    if (typeof messageData === 'object' && messageData !== null) {
      dataToSend = JSON.stringify(messageData);
      isJsonData = true;
    } else {
      dataToSend = messageData;
    }

    // 详细的调试日志
    log(`🔄 转发纯净数据到目标服务器:`);
    log(`   目标连接: #${targetConnectionId} (${connectionInfo.name})`);
    log(`   目标类型: ${connectionInfo.type}`);
    log(`   数据类型: ${isJsonData ? 'JSON' : '文本'}`);
    log(`   数据大小: ${dataToSend.length} 字节`);
    log(`   二进制模式: ${isBinary ? '是' : '否'}`);
    log(`   注意: 只转发原始数据，不包含项目包装字段`);

    // 打印要转发的数据内容
    if (isJsonData) {
      log(`   转发的 JSON 数据内容:`);
      try {
        // 美化 JSON 输出用于调试
        const prettyJson = JSON.stringify(JSON.parse(dataToSend), null, 2);
        log(`${prettyJson}`);
      } catch (e) {
        log(`   ${dataToSend}`);
      }
    } else {
      log(`   转发的文本数据内容: ${dataToSend.substring(0, 500)}${dataToSend.length > 500 ? '...' : ''}`);
    }

    connectionInfo.ws.send(dataToSend, { binary: isBinary });
    log(`✅ 消息已成功转发到目标服务器`);
    return true;

  } catch (error) {
    log(`❌ 转发消息到目标连接 #${targetConnectionId} 失败: ${error.message}`);
    return false;
  }
}

// 断开目标连接
function disconnectTarget(targetConnectionId) {
  const connectionInfo = targetConnections.get(targetConnectionId);
  if (!connectionInfo) {
    return false;
  }

  log(`断开目标连接 #${targetConnectionId}: ${connectionInfo.name}`);

  // 通知客户端
  broadcastToTargetClients(targetConnectionId, JSON.stringify({
    type: 'system',
    message: `目标服务器 ${connectionInfo.name} 即将断开连接`
  }));

  // 关闭连接
  if (connectionInfo.ws.readyState === WebSocket.OPEN) {
    connectionInfo.ws.close();
  }

  // 清理
  targetConnections.delete(targetConnectionId);
  clientGroups.delete(targetConnectionId);

  // 停止 Phoenix 心跳（如果有）
  stopPhoenixHeartbeat(targetConnectionId);

  return true;
}

// 向目标服务器发送消息（多目标模式下已废弃）
// function sendToTarget(data, isBinary = false) {
//   // 此函数在多目标模式下不再使用
//   return false;
// }

// WebSocket 客户端连接处理
wss.on('connection', (clientWs, request) => {
  const connId = ++clientId;
  const clientIP = request.socket.remoteAddress;

  log(`客户端 #${connId} 连接来自 ${clientIP}`);

  // 解析查询参数以获取目标连接ID
  const url = require('url');
  const query = url.parse(request.url, true).query;
  const targetId = query.target ? parseInt(query.target) : null;

  // 存储客户端信息
  const clientInfo = {
    ws: clientWs,
    ip: clientIP,
    startTime: Date.now(),
    messagesSent: 0,
    messagesReceived: 0,
    targetConnectionId: targetId
  };

  clients.set(connId, clientInfo);

  // 如果指定了目标连接，将客户端加入对应的组
  if (targetId && targetConnections.has(targetId)) {
    clientGroups.get(targetId).add(connId);
    const targetInfo = targetConnections.get(targetId);
    log(`客户端 #${connId} 连接到目标 #${targetId}: ${targetInfo.name}`);

    // 发送连接成功消息
    const statusMessage = JSON.stringify({
      type: 'system',
      message: `已连接到目标服务器: ${targetInfo.name}`,
      clientId: connId,
      targetId: targetId,
      targetName: targetInfo.name,
      targetType: targetInfo.type
    });
    clientWs.send(statusMessage);

    // 如果是 SignalR 连接且有缓存的初始化消息，立即发送给新客户端
    if (targetInfo.type === CONNECTION_TYPES.SIGNALR && targetInfo.initMessage) {
      setTimeout(() => {
        if (clientWs.readyState === WebSocket.OPEN) {
          clientWs.send(targetInfo.initMessage);
          log(`向客户端 #${connId} 发送了缓存的 SignalR 初始化消息`);
        }
      }, 100); // 稍微延迟确保连接稳定
    }
  } else {
    // 发送可用目标列表
    const availableTargets = getAvailableTargets();
    const statusMessage = JSON.stringify({
      type: 'system',
      message: '请选择要连接的目标服务器',
      clientId: connId,
      availableTargets: availableTargets,
      usage: 'ws://localhost:8080?target=目标ID'
    });
    clientWs.send(statusMessage);
  }

  // 处理客户端消息
  clientWs.on('message', (data, isBinary) => {
    const clientInfo = clients.get(connId);
    if (clientInfo) {
      clientInfo.messagesSent++;
    }

    log(`客户端 #${connId} 发送消息 (${data.length} 字节): ${data.toString().substring(0, 100)}...`);

    // 在一对多架构中，客户端消息不转发到目标服务器
    // 而是由代理服务器本身处理

    // 如果是 SignalR 模式，检查是否是特殊的控制消息
    if (CONFIG.SIGNALR_MODE && !isBinary) {
      try {
        const messageObj = JSON.parse(data.toString());

        // 检查是否是 SignalR 订阅消息（仅用于日志记录）
        if (messageObj.H && messageObj.M === "Subscribe" && messageObj.A) {
          log(`客户端 #${connId} 尝试发送 SignalR 订阅: ${JSON.stringify(messageObj.A)}`);

          // 通知客户端：订阅由服务器管理
          clientWs.send(JSON.stringify({
            type: 'system',
            message: '订阅由代理服务器统一管理，无需客户端发送订阅消息',
            note: '服务器已自动订阅所有 F1 数据流'
          }));
          return;
        }

        // 检查其他类型的控制消息
        if (messageObj.type === 'ping') {
          // 响应 ping 消息
          clientWs.send(JSON.stringify({
            type: 'pong',
            timestamp: Date.now()
          }));
          return;
        }

        if (messageObj.type === 'status') {
          // 响应状态查询
          const targets = getAvailableTargets();
          clientWs.send(JSON.stringify({
            type: 'status_response',
            clientId: connId,
            mode: 'Multi-Target',
            totalTargets: targets.length,
            connectedTargets: targets.filter(t => t.status === 'connected').length,
            totalClients: clients.size,
            availableTargets: targets.map(t => ({
              id: t.id,
              name: t.name,
              type: t.type,
              status: t.status
            }))
          }));
          return;
        }

        if (messageObj.type === 'forward_to_target') {
          // 转发消息到目标服务器
          log(`📨 客户端 #${connId} 请求转发消息:`);
          log(`   消息类型: ${typeof messageObj.data}`);

          if (typeof messageObj.data === 'object') {
            log(`   JSON 数据: ${JSON.stringify(messageObj.data, null, 2)}`);
          } else {
            log(`   文本数据: ${messageObj.data}`);
          }

          if (clientInfo.targetConnectionId) {
            log(`   目标连接: #${clientInfo.targetConnectionId}`);
            // 只转发 data 字段的内容，不包含项目的包装字段
            const success = forwardMessageToTarget(clientInfo.targetConnectionId, messageObj.data, isBinary);

            const response = {
              type: 'forward_response',
              success: success,
              message: success ? '消息已转发到目标服务器' : '消息转发失败',
              targetId: clientInfo.targetConnectionId,
              originalMessage: messageObj.data
            };

            log(`📤 向客户端 #${connId} 发送转发响应: ${success ? '成功' : '失败'}`);
            clientWs.send(JSON.stringify(response));
          } else {
            log(`❌ 客户端 #${connId} 未连接到任何目标服务器`);
            clientWs.send(JSON.stringify({
              type: 'forward_response',
              success: false,
              message: '客户端未连接到目标服务器',
              originalMessage: messageObj.data
            }));
          }
          return;
        }

        if (messageObj.type === 'signalr_subscribe') {
          // 手动触发 SignalR 订阅
          if (clientInfo.targetConnectionId) {
            const success = sendSignalRSubscriptionToTarget(clientInfo.targetConnectionId);
            clientWs.send(JSON.stringify({
              type: 'signalr_subscribe_response',
              success: success,
              message: success ? 'SignalR 订阅已发送' : 'SignalR 订阅发送失败',
              targetId: clientInfo.targetConnectionId
            }));
          } else {
            clientWs.send(JSON.stringify({
              type: 'signalr_subscribe_response',
              success: false,
              message: '客户端未连接到 SignalR 目标'
            }));
          }
          return;
        }

        // 检查是否是转发消息
        if (messageObj.hasOwnProperty('forward') && messageObj.forward === true) {
          log(`📨 在 SignalR 模式下收到转发消息: forward=${messageObj.forward}`);

          let messageToForward;
          if (messageObj.hasOwnProperty('data')) {
            messageToForward = typeof messageObj.data === 'object' ?
              JSON.stringify(messageObj.data) : messageObj.data;
          } else {
            const { forward, ...messageWithoutForward } = messageObj;
            messageToForward = JSON.stringify(messageWithoutForward);
          }

          log(`📤 将转发的内容: ${messageToForward}`);

          if (clientInfo.targetConnectionId) {
            const success = forwardMessageToTarget(clientInfo.targetConnectionId, messageToForward, isBinary);

            clientWs.send(JSON.stringify({
              type: 'auto_forward_response',
              success: success,
              message: success ? '消息已转发到目标服务器' : '转发失败',
              targetId: clientInfo.targetConnectionId,
              originalMessage: messageToForward.substring(0, 100) + (messageToForward.length > 100 ? '...' : '')
            }));
          } else {
            clientWs.send(JSON.stringify({
              type: 'system',
              message: '客户端未连接到目标服务器，无法转发消息',
              reason: '客户端未连接到目标服务器'
            }));
          }
          return;
        }

        // 检查是否是传统的 forward_to_target 消息
        if (messageObj.type === 'forward_to_target') {
          log(`📨 在 SignalR 模式下收到传统转发消息`);

          if (clientInfo.targetConnectionId) {
            const success = forwardMessageToTarget(clientInfo.targetConnectionId, messageObj.data, isBinary);

            clientWs.send(JSON.stringify({
              type: 'forward_response',
              success: success,
              message: success ? '消息已转发到目标服务器' : '消息转发失败',
              targetId: clientInfo.targetConnectionId,
              originalMessage: messageObj.data
            }));
          } else {
            clientWs.send(JSON.stringify({
              type: 'forward_response',
              success: false,
              message: '客户端未连接到目标服务器',
              originalMessage: messageObj.data
            }));
          }
          return;
        }

      } catch (e) {
        // 不是 JSON 格式，按普通消息处理
        log(`客户端 #${connId} 发送的不是标准 JSON 控制消息，将作为普通消息处理`);
        log(`🔍 调试信息 - 原始消息: ${data.toString()}`);
        log(`🔍 调试信息 - JSON 解析错误: ${e.message}`);
      }
    }

    // 对于普通消息，检查是否包含转发指令
    let shouldForward = false;
    let messageToForward = data.toString();

    // 尝试解析消息，检查是否包含转发字段
    try {
      log(`🔍 尝试解析消息进行转发检查: ${data.toString()}`);
      const parsedMessage = JSON.parse(data.toString());
      log(`🔍 JSON 解析成功，检查 forward 字段`);

      if (parsedMessage.hasOwnProperty('forward') && parsedMessage.forward === true) {
        shouldForward = true;
        // 如果有 data 字段，转发 data 内容；否则转发除了 forward 字段外的所有内容
        if (parsedMessage.hasOwnProperty('data')) {
          messageToForward = typeof parsedMessage.data === 'object' ?
            JSON.stringify(parsedMessage.data) : parsedMessage.data;
        } else {
          // 移除 forward 字段，转发其余内容
          const { forward, ...messageWithoutForward } = parsedMessage;
          messageToForward = JSON.stringify(messageWithoutForward);
        }
        log(`📨 消息包含转发指令: forward=${parsedMessage.forward}`);
        log(`📤 将转发的内容: ${messageToForward}`);
      } else {
        log(`🔍 消息不包含 forward: true，forward 值为: ${parsedMessage.forward}`);
      }
    } catch (e) {
      // 不是 JSON 格式，默认不转发
      log(`📝 收到普通文本消息，默认不转发（需要 JSON 格式的 forward 字段控制）`);
      log(`🔍 JSON 解析错误详情: ${e.message}`);
    }

    if (shouldForward && clientInfo.targetConnectionId) {
      // 转发消息到目标服务器
      log(`🔄 转发消息到目标服务器`);
      const success = forwardMessageToTarget(clientInfo.targetConnectionId, messageToForward, isBinary);

      clientWs.send(JSON.stringify({
        type: 'auto_forward_response',
        success: success,
        message: success ? '消息已转发到目标服务器' : '转发失败',
        targetId: clientInfo.targetConnectionId,
        originalMessage: messageToForward.substring(0, 100) + (messageToForward.length > 100 ? '...' : '')
      }));
    } else {
      // 不转发，提示用户
      let reason = '';
      if (!shouldForward) {
        reason = '消息未包含转发指令 (forward: true)';
      } else if (!clientInfo.targetConnectionId) {
        reason = '客户端未连接到目标服务器';
      }

      log(`❌ 不转发消息，原因: ${reason}`);

      clientWs.send(JSON.stringify({
        type: 'system',
        message: `已收到消息，但未转发。要转发消息，请在消息中添加 "forward": true 字段`,
        receivedLength: data.length,
        clientId: connId,
        reason: reason,
        examples: [
          '{"forward": true, "data": "your_message"}',
          '{"forward": true, "command": "getData", "params": {...}}',
          '{"type": "forward_to_target", "data": "your_message"}'
        ]
      }));
    }
  });

  // 处理客户端断开连接
  clientWs.on('close', (code, reason) => {
    log(`客户端 #${connId} 断开连接 (${code}: ${reason || '无'})`);

    // 从目标组中移除客户端
    if (clientInfo.targetConnectionId) {
      const targetGroup = clientGroups.get(clientInfo.targetConnectionId);
      if (targetGroup) {
        targetGroup.delete(connId);
      }
    }

    clients.delete(connId);
  });

  // 处理客户端错误
  clientWs.on('error', (error) => {
    log(`客户端 #${connId} 错误: ${error.message}`);

    // 从目标组中移除客户端
    if (clientInfo.targetConnectionId) {
      const targetGroup = clientGroups.get(clientInfo.targetConnectionId);
      if (targetGroup) {
        targetGroup.delete(connId);
      }
    }

    clients.delete(connId);
  });
});

// 静态文件服务
app.use(express.static('.'));

// 主页 - 提供测试客户端
app.get('/', (req, res) => {
  res.sendFile(__dirname + '/test-client.html');
});

// API 信息端点
app.get('/api', (req, res) => {
  // 获取目标连接信息
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');

  const targetInfo = {
    mode: 'Multi-Target',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    targets: targets.map(t => ({
      id: t.id,
      name: t.name,
      type: t.type,
      status: t.status
    }))
  };

  res.json({
    name: 'WebSocket 转发代理 (多目标模式)',
    version: '2.0.0',
    status: 'running',
    clientConnections: clients.size,
    ...targetInfo,
    uptime: process.uptime(),
    apiEndpoints: [
      'GET /',
      'GET /status',
      'GET /health',
      'GET /api/targets',
      'POST /api/reconnect/:type',
      'DELETE /api/disconnect/:type',
      'POST /signalr/subscribe',
      'GET /api/config'
    ]
  });
});

// 获取连接状态
app.get('/status', (req, res) => {
  const clientList = Array.from(clients.entries()).map(([id, client]) => ({
    id,
    ip: client.ip,
    uptime: Date.now() - client.startTime,
    messagesSent: client.messagesSent,
    messagesReceived: client.messagesReceived,
    status: client.ws.readyState === WebSocket.OPEN ? 'connected' : 'disconnected'
  }));

  // 获取目标连接信息
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');

  const targetInfo = {
    mode: 'Multi-Target',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    targets: targets.map(t => ({
      id: t.id,
      name: t.name,
      type: t.type,
      status: t.status
    }))
  };

  res.json({
    totalClients: clients.size,
    clients: clientList,
    targets: targetInfo,
    serverUptime: process.uptime()
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  const targets = getAvailableTargets();
  const connectedTargets = targets.filter(t => t.status === 'connected');
  const isHealthy = targets.length === 0 || connectedTargets.length > 0;

  res.status(isHealthy ? 200 : 503).json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    totalTargets: targets.length,
    connectedTargets: connectedTargets.length,
    clientConnections: clients.size,
    timestamp: new Date().toISOString()
  });
});

// SignalR 订阅端点
app.post('/signalr/subscribe', (req, res) => {
  const targets = getAvailableTargets();
  const signalrTargets = targets.filter(t => t.type === 'signalr' && t.status === 'connected');

  if (signalrTargets.length === 0) {
    return res.status(400).json({
      error: '没有可用的 SignalR 目标连接',
      message: '请先创建并连接 SignalR 目标'
    });
  }

  let successCount = 0;
  signalrTargets.forEach(target => {
    const success = sendSignalRSubscriptionToTarget(target.id);
    if (success) successCount++;
  });

  if (successCount > 0) {
    res.json({
      success: true,
      message: `已向 ${successCount} 个 SignalR 目标发送订阅消息`,
      targets: signalrTargets.map(t => ({ id: t.id, name: t.name })),
      timestamp: new Date().toISOString()
    });
  } else {
    res.status(503).json({
      success: false,
      error: '发送订阅消息失败',
      message: '所有 SignalR 目标连接不可用'
    });
  }
});

// 重新连接固定目标 API
app.post('/api/reconnect/:type', async (req, res) => {
  try {
    const { type } = req.params;

    if (type === 'signalr') {
      const config = {
        name: 'Fixed SignalR Connection',
        type: CONNECTION_TYPES.SIGNALR,
        fixedId: FIXED_CONNECTIONS.SIGNALR_ID,
        signalr: {
          url: CONFIG.SIGNALR_URL,
          hub: CONFIG.SIGNALR_HUB,
          protocol: CONFIG.SIGNALR_PROTOCOL
        }
      };

      const connectionInfo = await createTargetConnection(config);

      res.json({
        success: true,
        message: 'SignalR 连接重新建立成功',
        connection: {
          id: connectionInfo.id,
          name: connectionInfo.name,
          type: connectionInfo.type,
          timestamp: new Date().toISOString()
        }
      });
    } else if (type === 'websocket') {
      const config = {
        name: 'Fixed WebSocket Connection',
        type: CONNECTION_TYPES.WEBSOCKET,
        fixedId: FIXED_CONNECTIONS.WEBSOCKET_ID,
        url: CONFIG.TARGET_WS_URL
      };

      const connectionInfo = await createTargetConnection(config);

      res.json({
        success: true,
        message: 'WebSocket 连接重新建立成功',
        connection: {
          id: connectionInfo.id,
          name: connectionInfo.name,
          type: connectionInfo.type,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: '无效的连接类型，支持: signalr, websocket'
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 获取目标连接列表 API
app.get('/api/targets', (req, res) => {
  const targets = getAvailableTargets();
  res.json({
    success: true,
    targets: targets,
    count: targets.length,
    timestamp: new Date().toISOString()
  });
});

// 断开固定目标连接 API
app.delete('/api/disconnect/:type', (req, res) => {
  try {
    const { type } = req.params;
    let targetId;

    if (type === 'signalr') {
      targetId = FIXED_CONNECTIONS.SIGNALR_ID;
    } else if (type === 'websocket') {
      targetId = FIXED_CONNECTIONS.WEBSOCKET_ID;
    } else {
      return res.status(400).json({
        success: false,
        error: '无效的连接类型，支持: signalr, websocket'
      });
    }

    if (!targetConnections.has(targetId)) {
      return res.status(404).json({
        success: false,
        error: `${type} 连接不存在`
      });
    }

    const success = disconnectTarget(targetId);

    if (success) {
      res.json({
        success: true,
        message: `${type} 连接已断开`,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: '断开连接失败'
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取连接配置 API
app.get('/api/config', (req, res) => {
  const targets = getAvailableTargets();

  res.json({
    mode: 'Multi-Target',
    autoConnect: CONFIG.AUTO_CONNECT,
    signalrMode: CONFIG.SIGNALR_MODE,
    targets: {
      total: targets.length,
      connected: targets.filter(t => t.status === 'connected').length,
      byType: {
        websocket: targets.filter(t => t.type === 'websocket').length,
        signalr: targets.filter(t => t.type === 'signalr').length
      }
    },
    clients: {
      total: clients.size,
      byTarget: Array.from(clientGroups.entries()).map(([targetId, clientSet]) => ({
        targetId,
        clientCount: clientSet.size
      }))
    },
    security: {
      apiKeyEnabled: !!CONFIG.API_KEY,
      corsEnabled: true,
      rateLimitEnabled: true
    },
    environment: {
      AUTO_CONNECT: process.env.AUTO_CONNECT || 'undefined'
    },
    timestamp: new Date().toISOString()
  });
});

// 更新配置 API
app.put('/api/config', (req, res) => {
  try {
    const { autoReconnect } = req.body;

    if (typeof autoReconnect === 'boolean') {
      dynamicConfig.autoReconnect = autoReconnect;
      console.log(`🔄 动态更新自动重连模式: ${autoReconnect ? '启用' : '禁用'}`);
    }

    res.json({
      success: true,
      message: '配置已更新',
      config: {
        ...dynamicConfig
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
server.listen(CONFIG.PORT, () => {
  console.log(`🚀 WebSocket 转发代理服务器 (固定双连接模式) 启动在端口 ${CONFIG.PORT}`);
  console.log(`🌐 Web 测试客户端: http://localhost:${CONFIG.PORT}`);
  console.log(`📡 WebSocket 连接地址:`);
  console.log(`   - SignalR 连接: ws://localhost:${CONFIG.PORT}?target=${FIXED_CONNECTIONS.SIGNALR_ID}`);
  console.log(`   - WebSocket 连接: ws://localhost:${CONFIG.PORT}?target=${FIXED_CONNECTIONS.WEBSOCKET_ID}`);
  console.log(`📊 状态页面: http://localhost:${CONFIG.PORT}/status`);
  console.log(`💚 健康检查: http://localhost:${CONFIG.PORT}/health`);
  console.log(`🔧 API 端点:`);
  console.log(`   - GET /api - API 信息`);
  console.log(`   - GET /api/targets - 获取目标连接列表`);
  console.log(`   - POST /api/reconnect/:type - 重新连接固定目标 (signalr/websocket)`);
  console.log(`   - DELETE /api/disconnect/:type - 断开固定目标 (signalr/websocket)`);
  console.log(`   - GET /api/config - 获取配置`);
  console.log(`   - PUT /api/config - 更新配置`);

  // 自动创建两个固定连接
  console.log(`\n🔄 正在创建固定连接...`);

  // 创建固定的 SignalR 连接 (ID: 1)
  console.log(`🎯 SignalR 配置:`);
  console.log(`   - SignalR URL: ${CONFIG.SIGNALR_URL}`);
  console.log(`   - Hub: ${CONFIG.SIGNALR_HUB}`);
  console.log(`   - Protocol: ${CONFIG.SIGNALR_PROTOCOL}`);
  console.log(`   - 固定 ID: ${FIXED_CONNECTIONS.SIGNALR_ID}`);

  createTargetConnection({
    name: 'Fixed SignalR Connection',
    type: CONNECTION_TYPES.SIGNALR,
    fixedId: FIXED_CONNECTIONS.SIGNALR_ID,
    signalr: {
      url: CONFIG.SIGNALR_URL,
      hub: CONFIG.SIGNALR_HUB,
      protocol: CONFIG.SIGNALR_PROTOCOL
    }
  }).catch(error => {
    console.error(`❌ 创建固定 SignalR 连接失败: ${error.message}`);
  });

  // 创建固定的 WebSocket 连接 (ID: 2)
  console.log(`🎯 WebSocket 配置:`);
  console.log(`   - 目标 URL: ${CONFIG.TARGET_WS_URL}`);
  console.log(`   - 固定 ID: ${FIXED_CONNECTIONS.WEBSOCKET_ID}`);

  createTargetConnection({
    name: 'Fixed WebSocket Connection',
    type: CONNECTION_TYPES.WEBSOCKET,
    fixedId: FIXED_CONNECTIONS.WEBSOCKET_ID,
    url: CONFIG.TARGET_WS_URL
  }).catch(error => {
    console.error(`❌ 创建固定 WebSocket 连接失败: ${error.message}`);
  });

  // 显示配置信息
  console.log(`\n⚙️  服务器配置:`);
  console.log(`   连接模式: 固定双连接 (SignalR ID: ${FIXED_CONNECTIONS.SIGNALR_ID}, WebSocket ID: ${FIXED_CONNECTIONS.WEBSOCKET_ID})`);
  console.log(`   自动连接: 启用 (固定连接)`);
  console.log(`   转发控制: 基于消息字段 (forward: true/false)`);

  console.log(`\n� 转发模式说明:`);
  console.log(`   - 使用 {"forward": true, "data": "..."} 转发消息`);
  console.log(`   - 使用 {"type": "forward_to_target", "data": "..."} 手动转发`);
  console.log(`   - 没有 forward 字段的消息默认不转发`);

  console.log(`\n🔗 连接说明:`);
  console.log(`   - 客户端连接到 SignalR: ws://localhost:${CONFIG.PORT}?target=1`);
  console.log(`   - 客户端连接到 WebSocket: ws://localhost:${CONFIG.PORT}?target=2`);
});

// 优雅关闭
function gracefulShutdown(signal) {
  console.log(`收到 ${signal} 信号，正在关闭服务器...`);

  // 清除重连定时器（兼容旧代码）
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  // 关闭所有目标连接
  console.log(`正在关闭 ${targetConnections.size} 个目标连接...`);
  targetConnections.forEach((connectionInfo, id) => {
    // 停止 Phoenix 心跳
    stopPhoenixHeartbeat(id);

    if (connectionInfo.ws && connectionInfo.ws.readyState === WebSocket.OPEN) {
      console.log(`关闭目标连接 #${id}: ${connectionInfo.name}`);
      connectionInfo.ws.close();
    }
  });

  // 关闭旧的目标连接（兼容旧代码）
  if (targetConnection && targetConnection.readyState === WebSocket.OPEN) {
    console.log('正在关闭旧的目标服务器连接...');
    targetConnection.close();
  }

  // 通知所有客户端服务器即将关闭
  console.log(`正在通知 ${clients.size} 个客户端...`);
  broadcastToClients(JSON.stringify({
    type: 'system',
    message: '服务器即将关闭'
  }));

  // 等待一秒让消息发送完成，然后关闭所有客户端连接
  setTimeout(() => {
    clients.forEach((client, id) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1001, '服务器关闭');
      }
    });
    clients.clear();

    // 关闭 HTTP 服务器
    server.close(() => {
      console.log('服务器已关闭');
      process.exit(0);
    });
  }, 1000);
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
