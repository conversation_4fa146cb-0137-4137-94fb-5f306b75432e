const WebSocket = require('ws');

// 测试新的 SignalR 订阅逻辑
async function testSignalRSubscription() {
  console.log('🧪 测试 SignalR 订阅逻辑...');
  
  // 连接到代理服务器的 SignalR 目标 (ID: 1)
  const ws = new WebSocket('ws://localhost:8080?target=1');
  
  ws.on('open', () => {
    console.log('✅ 已连接到代理服务器 SignalR 目标');
    console.log('⏳ 等待代理服务器代替客户端发送订阅消息...');
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'system') {
        console.log(`📢 系统消息: ${message.message}`);
        
        if (message.message.includes('代理服务器已代替您发送 SignalR 订阅消息')) {
          console.log('✅ 代理服务器已代替客户端发送订阅消息');
          console.log('📋 订阅内容:', message.subscription);
        }
      } else {
        console.log('📥 收到数据:', {
          type: typeof message,
          size: data.length,
          preview: data.toString().substring(0, 100) + (data.length > 100 ? '...' : '')
        });
      }
    } catch (e) {
      console.log('📥 收到非 JSON 数据:', {
        size: data.length,
        preview: data.toString().substring(0, 100) + (data.length > 100 ? '...' : '')
      });
    }
  });
  
  ws.on('close', (code, reason) => {
    console.log(`❌ 连接关闭: ${code} - ${reason}`);
  });
  
  ws.on('error', (error) => {
    console.error('❌ 连接错误:', error.message);
  });
  
  // 10秒后关闭连接
  setTimeout(() => {
    console.log('⏰ 测试时间结束，关闭连接');
    ws.close();
  }, 10000);
}

// 测试多个客户端连接
async function testMultipleClients() {
  console.log('\n🧪 测试多个客户端连接...');
  
  const clients = [];
  
  for (let i = 1; i <= 3; i++) {
    const ws = new WebSocket('ws://localhost:8080?target=1');
    clients.push(ws);
    
    ws.on('open', () => {
      console.log(`✅ 客户端 ${i} 已连接到 SignalR 目标`);
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        if (message.type === 'system' && message.message.includes('代理服务器已代替您发送')) {
          console.log(`📢 客户端 ${i} 收到订阅确认`);
        }
      } catch (e) {
        // 忽略非 JSON 消息
      }
    });
    
    // 延迟连接下一个客户端
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 5秒后关闭所有连接
  setTimeout(() => {
    console.log('⏰ 关闭所有测试客户端');
    clients.forEach(ws => ws.close());
  }, 5000);
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试新的 SignalR 订阅逻辑');
  console.log('📝 测试说明:');
  console.log('   1. 当新客户端连接到 SignalR 目标时');
  console.log('   2. 代理服务器应该代替客户端发送订阅消息');
  console.log('   3. 订阅响应应该发送给该特定客户端');
  console.log('   4. 其他数据继续广播给所有客户端');
  console.log('');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试单个客户端
  await testSignalRSubscription();
  
  // 等待一段时间
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 测试多个客户端
  await testMultipleClients();
  
  // 等待测试完成
  await new Promise(resolve => setTimeout(resolve, 8000));
  
  console.log('\n✅ 测试完成');
  process.exit(0);
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});

// 运行测试
runTests().catch(console.error);
