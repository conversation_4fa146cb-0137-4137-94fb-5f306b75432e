# 固定双连接模式修改说明

## 概述

项目已成功修改为固定双连接模式，只连接一个 SignalR 和一个 WebSocket，它们的 ID 固定。

## 主要修改

### 1. 服务器端修改 (server.js)

#### 固定连接 ID 定义
```javascript
const FIXED_CONNECTIONS = {
  SIGNALR_ID: 1,
  WEBSOCKET_ID: 2
};
```

#### 配置简化
- 移除了 `SIGNALR_MODE` 配置
- 固定 `AUTO_CONNECT` 为 true
- 简化了动态配置对象

#### 连接管理修改
- `createTargetConnection` 函数支持固定 ID
- 自动检查并断开已存在的相同 ID 连接
- 启动时自动创建两个固定连接

#### API 端点修改
- 移除了 `POST /api/targets` (创建目标连接)
- 移除了 `DELETE /api/targets/:id` (删除目标连接)
- 新增了 `POST /api/reconnect/:type` (重连固定目标)
- 新增了 `DELETE /api/disconnect/:type` (断开固定目标)

#### 启动逻辑修改
- 自动创建 SignalR 连接 (ID: 1)
- 自动创建 WebSocket 连接 (ID: 2)
- 更新了控制台输出信息

### 2. 客户端修改 (test-client.html)

#### 目标选择简化
- 固定了目标选择下拉框选项
- 预设 SignalR (ID: 1) 和 WebSocket (ID: 2) 选项

#### 管理界面修改
- 移除了创建新目标连接的表单
- 替换为固定连接状态显示
- 添加了重连和断开按钮

#### JavaScript 函数修改
- 移除了 `createTarget()` 函数
- 移除了 `deleteTarget()` 函数
- 新增了 `reconnectSignalR()` 函数
- 新增了 `reconnectWebSocket()` 函数
- 新增了 `disconnectSignalR()` 函数
- 新增了 `disconnectWebSocket()` 函数

### 3. 文档修改 (README.md)

- 更新了项目标题为"固定双连接模式"
- 修改了架构特点说明
- 更新了功能描述

## 使用方法

### 启动服务器
```bash
npm start
```

### 客户端连接
- 连接到 SignalR: `ws://localhost:8080?target=1`
- 连接到 WebSocket: `ws://localhost:8080?target=2`

### Web 界面
访问 `http://localhost:8080` 使用测试客户端

### API 端点
- `GET /api/targets` - 获取固定连接状态
- `POST /api/reconnect/signalr` - 重连 SignalR
- `POST /api/reconnect/websocket` - 重连 WebSocket
- `DELETE /api/disconnect/signalr` - 断开 SignalR
- `DELETE /api/disconnect/websocket` - 断开 WebSocket

## 配置

### 环境变量
- `PORT` - 服务器端口 (默认: 8080)
- `TARGET_WS_URL` - WebSocket 目标 URL (默认: ws://echo.websocket.org)
- `SIGNALR_URL` - SignalR 服务器 URL (默认: livetiming.formula1.com/signalr)
- `SIGNALR_HUB` - SignalR Hub 名称 (默认: Streaming)
- `SIGNALR_PROTOCOL` - SignalR 协议版本 (默认: 1.5)

### 固定连接
- **ID 1**: SignalR 连接 - 连接到 F1 实时数据服务器
- **ID 2**: WebSocket 连接 - 连接到通用 WebSocket 服务器

## 特性

- ✅ 固定双连接架构
- ✅ 自动连接管理
- ✅ 客户端目标选择
- ✅ 双向消息转发
- ✅ SignalR 协议支持
- ✅ Phoenix Channel 心跳支持
- ✅ 自动重连机制
- ✅ Web 管理界面
- ✅ RESTful API

## 注意事项

1. 服务器启动时会自动创建两个固定连接
2. 如果目标服务器不可用，连接会失败但不影响服务器运行
3. 客户端必须指定 target 参数来连接到特定的目标
4. 不指定 target 参数的客户端会收到可用目标列表
